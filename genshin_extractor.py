#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
原神资源提取工具 - Python GUI版本
基于 RazTools Studio v1.36.00
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import subprocess
import threading
import sys
from pathlib import Path

class GenshinExtractor:
    def __init__(self, root):
        self.root = root
        self.root.title("原神资源提取工具 - RazTools Studio")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 设置变量
        self.genshin_path = tk.StringVar()
        self.output_path = tk.StringVar(value="./GenshinOutput")
        self.resource_type = tk.StringVar(value="all")
        self.filter_text = tk.StringVar()
        
        # 检查 AssetStudio 是否存在
        self.assetstudio_path = "./AssetStudio/AssetStudio.CLI.exe"
        if not os.path.exists(self.assetstudio_path):
            messagebox.showerror("错误", "找不到 AssetStudio.CLI.exe\n请确保 AssetStudio 已正确解压到 ./AssetStudio/ 目录")
            sys.exit(1)
        
        self.setup_ui()
        self.auto_detect_genshin_path()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="原神资源提取工具", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        subtitle_label = ttk.Label(main_frame, text="基于 RazTools Studio v1.36.00", font=("Arial", 10))
        subtitle_label.grid(row=1, column=0, columnspan=3, pady=(0, 20))
        
        # 原神路径选择
        ttk.Label(main_frame, text="原神路径:").grid(row=2, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.genshin_path, width=60).grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 5))
        ttk.Button(main_frame, text="浏览", command=self.browse_genshin_path).grid(row=2, column=2, pady=5)
        
        # 输出路径选择
        ttk.Label(main_frame, text="输出路径:").grid(row=3, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.output_path, width=60).grid(row=3, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 5))
        ttk.Button(main_frame, text="浏览", command=self.browse_output_path).grid(row=3, column=2, pady=5)
        
        # 资源类型选择
        ttk.Label(main_frame, text="资源类型:").grid(row=4, column=0, sticky=tk.W, pady=5)
        resource_frame = ttk.Frame(main_frame)
        resource_frame.grid(row=4, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        resource_types = [
            ("all", "所有资源"),
            ("textures", "贴图 (Texture2D)"),
            ("models", "模型 (Mesh)"),
            ("audio", "音频 (AudioClip)"),
            ("animations", "动画 (AnimationClip)"),
            ("materials", "材质 (Material)"),
            ("sprites", "精灵 (Sprite)"),
            ("fonts", "字体 (Font)"),
            ("shaders", "着色器 (Shader)"),
            ("assetmap", "资源映射表")
        ]
        
        for i, (value, text) in enumerate(resource_types):
            row = i // 2
            col = i % 2
            ttk.Radiobutton(resource_frame, text=text, variable=self.resource_type, value=value).grid(
                row=row, column=col, sticky=tk.W, padx=(0, 20), pady=2
            )
        
        # 过滤器
        ttk.Label(main_frame, text="名称过滤器:").grid(row=5, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.filter_text, width=60).grid(row=5, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 5))
        ttk.Label(main_frame, text="(正则表达式，可选)").grid(row=5, column=2, pady=5)
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=6, column=0, columnspan=3, pady=20)
        
        ttk.Button(button_frame, text="开始提取", command=self.start_extraction).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="打开GUI版本", command=self.open_gui).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="清空日志", command=self.clear_log).pack(side=tk.LEFT, padx=5)
        
        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=7, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        
        # 日志输出
        ttk.Label(main_frame, text="输出日志:").grid(row=8, column=0, sticky=tk.W, pady=(10, 5))
        self.log_text = scrolledtext.ScrolledText(main_frame, height=15, width=80)
        self.log_text.grid(row=9, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        
        # 配置日志区域的网格权重
        main_frame.rowconfigure(9, weight=1)
    
    def auto_detect_genshin_path(self):
        """自动检测原神路径"""
        possible_paths = [
            r"C:\Program Files\miHoYo Launcher\games\Genshin Impact Game\YuanShen_Data\StreamingAssets\AssetBundles",
            r"C:\Program Files\Genshin Impact\Genshin Impact Game\YuanShen_Data\StreamingAssets\AssetBundles",
            r"C:\Program Files\Genshin Impact\Genshin Impact Game\GenshinImpact_Data\StreamingAssets\AssetBundles",
            r"D:\Program Files\Genshin Impact\Genshin Impact Game\YuanShen_Data\StreamingAssets\AssetBundles",
            r"D:\Program Files\Genshin Impact\Genshin Impact Game\GenshinImpact_Data\StreamingAssets\AssetBundles"
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                self.genshin_path.set(path)
                self.log(f"自动检测到原神路径: {path}")
                return
        
        self.log("未自动检测到原神路径，请手动选择")
    
    def browse_genshin_path(self):
        """浏览选择原神路径"""
        path = filedialog.askdirectory(title="选择原神 AssetBundles 文件夹")
        if path:
            self.genshin_path.set(path)
    
    def browse_output_path(self):
        """浏览选择输出路径"""
        path = filedialog.askdirectory(title="选择输出文件夹")
        if path:
            self.output_path.set(path)
    
    def log(self, message):
        """添加日志消息"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
    
    def validate_inputs(self):
        """验证输入"""
        if not self.genshin_path.get():
            messagebox.showerror("错误", "请选择原神路径")
            return False
        
        if not os.path.exists(self.genshin_path.get()):
            messagebox.showerror("错误", "原神路径不存在")
            return False
        
        if not self.output_path.get():
            messagebox.showerror("错误", "请选择输出路径")
            return False
        
        return True
    
    def build_command(self):
        """构建命令行参数"""
        cmd = [self.assetstudio_path, self.genshin_path.get(), self.output_path.get(), "--game", "GI"]
        
        # 添加资源类型参数
        resource_type = self.resource_type.get()
        if resource_type == "textures":
            cmd.extend(["--types", "Texture2D"])
        elif resource_type == "models":
            cmd.extend(["--types", "Mesh"])
        elif resource_type == "audio":
            cmd.extend(["--types", "AudioClip"])
        elif resource_type == "animations":
            cmd.extend(["--types", "AnimationClip"])
        elif resource_type == "materials":
            cmd.extend(["--types", "Material"])
        elif resource_type == "sprites":
            cmd.extend(["--types", "Sprite"])
        elif resource_type == "fonts":
            cmd.extend(["--types", "Font"])
        elif resource_type == "shaders":
            cmd.extend(["--types", "Shader"])
        elif resource_type == "assetmap":
            cmd.extend(["--map_op", "AssetMap", "--map_type", "JSON"])
        
        # 添加过滤器
        if self.filter_text.get().strip():
            cmd.extend(["--names", self.filter_text.get().strip()])
        
        return cmd
    
    def run_extraction(self):
        """运行提取过程"""
        try:
            # 创建输出目录
            os.makedirs(self.output_path.get(), exist_ok=True)
            
            cmd = self.build_command()
            self.log(f"执行命令: {' '.join(cmd)}")
            self.log("开始提取资源...")
            
            # 运行命令
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                encoding='utf-8',
                errors='replace'
            )
            
            # 实时输出日志
            for line in process.stdout:
                self.log(line.strip())
            
            process.wait()
            
            if process.returncode == 0:
                self.log("✅ 资源提取完成!")
                messagebox.showinfo("完成", f"资源提取完成!\n输出位置: {self.output_path.get()}")
            else:
                self.log(f"❌ 提取失败，退出代码: {process.returncode}")
                messagebox.showerror("错误", f"提取失败，退出代码: {process.returncode}")
        
        except Exception as e:
            self.log(f"❌ 发生错误: {str(e)}")
            messagebox.showerror("错误", f"发生错误: {str(e)}")
        
        finally:
            self.progress.stop()
    
    def start_extraction(self):
        """开始提取（在新线程中运行）"""
        if not self.validate_inputs():
            return
        
        self.progress.start()
        self.log("=" * 50)
        self.log("开始新的提取任务")
        
        # 在新线程中运行提取过程
        thread = threading.Thread(target=self.run_extraction)
        thread.daemon = True
        thread.start()
    
    def open_gui(self):
        """打开 AssetStudio GUI 版本"""
        gui_path = "./AssetStudio/AssetStudio.GUI.exe"
        if os.path.exists(gui_path):
            try:
                subprocess.Popen([gui_path])
                self.log("已启动 AssetStudio GUI 版本")
                messagebox.showinfo("提示", "GUI 版本已启动\n请在界面中选择游戏为 GI (Genshin Impact)")
            except Exception as e:
                messagebox.showerror("错误", f"无法启动 GUI 版本: {str(e)}")
        else:
            messagebox.showerror("错误", "找不到 AssetStudio.GUI.exe")

def main():
    root = tk.Tk()
    app = GenshinExtractor(root)
    root.mainloop()

if __name__ == "__main__":
    main()
