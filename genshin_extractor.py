#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
原神资源提取工具 - Python GUI版本
基于 RazTools Studio v1.36.00
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import subprocess
import threading
import sys
from pathlib import Path

class GenshinExtractor:
    def __init__(self, root):
        self.root = root
        self.root.title("原神资源提取工具 - RazTools Studio")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 设置变量
        self.genshin_path = tk.StringVar()
        self.output_path = tk.StringVar(value="./GenshinOutput")
        self.extract_all = tk.BooleanVar(value=True)
        self.filter_text = tk.StringVar()

        # 资源类型复选框变量
        self.extract_textures = tk.BooleanVar()
        self.extract_models = tk.BooleanVar()
        self.extract_audio = tk.BooleanVar()
        self.extract_animations = tk.BooleanVar()
        self.extract_materials = tk.BooleanVar()
        self.extract_sprites = tk.BooleanVar()
        self.extract_fonts = tk.BooleanVar()
        self.extract_shaders = tk.BooleanVar()
        self.extract_assetmap = tk.BooleanVar()
        self.extract_blk_files = tk.BooleanVar()

        # 高级选项
        self.use_asset_index = tk.BooleanVar()
        self.asset_index_path = tk.StringVar()
        self.decrypt_key = tk.StringVar()
        
        # 检查 AssetStudio 是否存在
        self.assetstudio_path = "./AssetStudio/AssetStudio.CLI.exe"
        if not os.path.exists(self.assetstudio_path):
            messagebox.showerror("错误", "找不到 AssetStudio.CLI.exe\n请确保 AssetStudio 已正确解压到 ./AssetStudio/ 目录")
            sys.exit(1)
        
        self.setup_ui()
        self.auto_detect_genshin_path()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="原神资源提取工具", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        subtitle_label = ttk.Label(main_frame, text="基于 RazTools Studio v1.36.00", font=("Arial", 10))
        subtitle_label.grid(row=1, column=0, columnspan=3, pady=(0, 20))
        
        # 原神路径选择
        ttk.Label(main_frame, text="原神路径:").grid(row=2, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.genshin_path, width=60).grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 5))
        ttk.Button(main_frame, text="浏览", command=self.browse_genshin_path).grid(row=2, column=2, pady=5)
        
        # 输出路径选择
        ttk.Label(main_frame, text="输出路径:").grid(row=3, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.output_path, width=60).grid(row=3, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 5))
        ttk.Button(main_frame, text="浏览", command=self.browse_output_path).grid(row=3, column=2, pady=5)
        
        # 提取模式选择
        ttk.Label(main_frame, text="提取模式:").grid(row=4, column=0, sticky=tk.W, pady=5)
        mode_frame = ttk.Frame(main_frame)
        mode_frame.grid(row=4, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=5)

        ttk.Radiobutton(mode_frame, text="提取所有资源", variable=self.extract_all, value=True,
                       command=self.on_mode_change).grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Radiobutton(mode_frame, text="选择特定资源类型", variable=self.extract_all, value=False,
                       command=self.on_mode_change).grid(row=0, column=1, sticky=tk.W, padx=(20, 0), pady=2)

        # 资源类型复选框
        ttk.Label(main_frame, text="资源类型:").grid(row=5, column=0, sticky=tk.W, pady=5)
        self.resource_frame = ttk.Frame(main_frame)
        self.resource_frame.grid(row=5, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=5)

        resource_checkboxes = [
            (self.extract_textures, "贴图 (Texture2D)"),
            (self.extract_models, "模型 (Mesh)"),
            (self.extract_audio, "音频 (AudioClip)"),
            (self.extract_animations, "动画 (AnimationClip)"),
            (self.extract_materials, "材质 (Material)"),
            (self.extract_sprites, "精灵 (Sprite)"),
            (self.extract_fonts, "字体 (Font)"),
            (self.extract_shaders, "着色器 (Shader)"),
            (self.extract_blk_files, "BLK文件 (原始)"),
            (self.extract_assetmap, "资源映射表")
        ]

        for i, (var, text) in enumerate(resource_checkboxes):
            row = i // 3
            col = i % 3
            ttk.Checkbutton(self.resource_frame, text=text, variable=var).grid(
                row=row, column=col, sticky=tk.W, padx=(0, 15), pady=2
            )

        # 初始状态设置
        self.on_mode_change()
        
        # 过滤器
        ttk.Label(main_frame, text="名称过滤器:").grid(row=6, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.filter_text, width=60).grid(row=6, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 5))
        ttk.Label(main_frame, text="(正则表达式，可选)").grid(row=6, column=2, pady=5)

        # 高级选项
        advanced_frame = ttk.LabelFrame(main_frame, text="高级选项 (用于BLK文件解密)", padding="10")
        advanced_frame.grid(row=7, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)

        ttk.Checkbutton(advanced_frame, text="使用 asset_index 文件恢复容器信息",
                       variable=self.use_asset_index).grid(row=0, column=0, sticky=tk.W, pady=2)

        ttk.Label(advanced_frame, text="asset_index 文件路径:").grid(row=1, column=0, sticky=tk.W, pady=2)
        ttk.Entry(advanced_frame, textvariable=self.asset_index_path, width=50).grid(row=1, column=1, sticky=(tk.W, tk.E), pady=2, padx=(5, 5))
        ttk.Button(advanced_frame, text="浏览", command=self.browse_asset_index).grid(row=1, column=2, pady=2)

        ttk.Label(advanced_frame, text="解密密钥 (可选):").grid(row=2, column=0, sticky=tk.W, pady=2)
        ttk.Entry(advanced_frame, textvariable=self.decrypt_key, width=50).grid(row=2, column=1, sticky=(tk.W, tk.E), pady=2, padx=(5, 5))

        advanced_frame.columnconfigure(1, weight=1)
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=8, column=0, columnspan=3, pady=20)

        ttk.Button(button_frame, text="开始提取", command=self.start_extraction).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="打开GUI版本", command=self.open_gui).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="清空日志", command=self.clear_log).pack(side=tk.LEFT, padx=5)

        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=8, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)

        # 日志输出
        ttk.Label(main_frame, text="输出日志:").grid(row=9, column=0, sticky=tk.W, pady=(10, 5))
        self.log_text = scrolledtext.ScrolledText(main_frame, height=15, width=80)
        self.log_text.grid(row=10, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)

        # 配置日志区域的网格权重
        main_frame.rowconfigure(10, weight=1)
    
    def auto_detect_genshin_path(self):
        """自动检测原神路径"""
        possible_paths = [
            r"C:\Program Files\miHoYo Launcher\games\Genshin Impact Game\YuanShen_Data\StreamingAssets\AssetBundles",
            r"C:\Program Files\Genshin Impact\Genshin Impact Game\YuanShen_Data\StreamingAssets\AssetBundles",
            r"C:\Program Files\Genshin Impact\Genshin Impact Game\GenshinImpact_Data\StreamingAssets\AssetBundles",
            r"D:\Program Files\Genshin Impact\Genshin Impact Game\YuanShen_Data\StreamingAssets\AssetBundles",
            r"D:\Program Files\Genshin Impact\Genshin Impact Game\GenshinImpact_Data\StreamingAssets\AssetBundles"
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                self.genshin_path.set(path)
                self.log(f"自动检测到原神路径: {path}")
                return
        
        self.log("未自动检测到原神路径，请手动选择")

    def on_mode_change(self):
        """模式切换时的处理"""
        if self.extract_all.get():
            # 禁用复选框
            for widget in self.resource_frame.winfo_children():
                widget.configure(state='disabled')
        else:
            # 启用复选框
            for widget in self.resource_frame.winfo_children():
                widget.configure(state='normal')
    
    def browse_genshin_path(self):
        """浏览选择原神路径"""
        path = filedialog.askdirectory(title="选择原神 AssetBundles 文件夹")
        if path:
            self.genshin_path.set(path)
    
    def browse_output_path(self):
        """浏览选择输出路径"""
        path = filedialog.askdirectory(title="选择输出文件夹")
        if path:
            self.output_path.set(path)
    
    def log(self, message):
        """添加日志消息"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
    
    def validate_inputs(self):
        """验证输入"""
        if not self.genshin_path.get():
            messagebox.showerror("错误", "请选择原神路径")
            return False

        if not os.path.exists(self.genshin_path.get()):
            messagebox.showerror("错误", "原神路径不存在")
            return False

        if not self.output_path.get():
            messagebox.showerror("错误", "请选择输出路径")
            return False

        # 如果选择特定资源类型，检查是否至少选择了一个
        if not self.extract_all.get():
            selected = any([
                self.extract_textures.get(),
                self.extract_models.get(),
                self.extract_audio.get(),
                self.extract_animations.get(),
                self.extract_materials.get(),
                self.extract_sprites.get(),
                self.extract_fonts.get(),
                self.extract_shaders.get(),
                self.extract_assetmap.get()
            ])
            if not selected:
                messagebox.showerror("错误", "请至少选择一种资源类型")
                return False

        return True
    
    def get_extraction_tasks(self):
        """获取提取任务列表"""
        tasks = []

        if self.extract_all.get():
            # 提取所有资源
            cmd = [self.assetstudio_path, self.genshin_path.get(), self.output_path.get(), "--game", "GI"]
            if self.filter_text.get().strip():
                cmd.extend(["--names", self.filter_text.get().strip()])
            tasks.append(("所有资源", cmd))
        else:
            # 根据选择的类型创建多个任务
            base_path = self.output_path.get()

            if self.extract_textures.get():
                cmd = [self.assetstudio_path, self.genshin_path.get(), f"{base_path}/Textures", "--game", "GI", "--types", "Texture2D"]
                if self.filter_text.get().strip():
                    cmd.extend(["--names", self.filter_text.get().strip()])
                tasks.append(("贴图", cmd))

            if self.extract_models.get():
                cmd = [self.assetstudio_path, self.genshin_path.get(), f"{base_path}/Models", "--game", "GI", "--types", "Mesh"]
                if self.filter_text.get().strip():
                    cmd.extend(["--names", self.filter_text.get().strip()])
                tasks.append(("模型", cmd))

            if self.extract_audio.get():
                cmd = [self.assetstudio_path, self.genshin_path.get(), f"{base_path}/Audio", "--game", "GI", "--types", "AudioClip"]
                if self.filter_text.get().strip():
                    cmd.extend(["--names", self.filter_text.get().strip()])
                tasks.append(("音频", cmd))

            if self.extract_animations.get():
                cmd = [self.assetstudio_path, self.genshin_path.get(), f"{base_path}/Animations", "--game", "GI", "--types", "AnimationClip"]
                if self.filter_text.get().strip():
                    cmd.extend(["--names", self.filter_text.get().strip()])
                tasks.append(("动画", cmd))

            if self.extract_materials.get():
                cmd = [self.assetstudio_path, self.genshin_path.get(), f"{base_path}/Materials", "--game", "GI", "--types", "Material"]
                if self.filter_text.get().strip():
                    cmd.extend(["--names", self.filter_text.get().strip()])
                tasks.append(("材质", cmd))

            if self.extract_sprites.get():
                cmd = [self.assetstudio_path, self.genshin_path.get(), f"{base_path}/Sprites", "--game", "GI", "--types", "Sprite"]
                if self.filter_text.get().strip():
                    cmd.extend(["--names", self.filter_text.get().strip()])
                tasks.append(("精灵", cmd))

            if self.extract_fonts.get():
                cmd = [self.assetstudio_path, self.genshin_path.get(), f"{base_path}/Fonts", "--game", "GI", "--types", "Font"]
                if self.filter_text.get().strip():
                    cmd.extend(["--names", self.filter_text.get().strip()])
                tasks.append(("字体", cmd))

            if self.extract_shaders.get():
                cmd = [self.assetstudio_path, self.genshin_path.get(), f"{base_path}/Shaders", "--game", "GI", "--types", "Shader"]
                if self.filter_text.get().strip():
                    cmd.extend(["--names", self.filter_text.get().strip()])
                tasks.append(("着色器", cmd))

            if self.extract_assetmap.get():
                cmd = [self.assetstudio_path, self.genshin_path.get(), base_path, "--game", "GI", "--map_op", "AssetMap", "--map_type", "JSON"]
                tasks.append(("资源映射表", cmd))

        return tasks
    
    def run_extraction(self):
        """运行提取过程"""
        try:
            # 创建输出目录
            os.makedirs(self.output_path.get(), exist_ok=True)

            tasks = self.get_extraction_tasks()
            total_tasks = len(tasks)

            self.log(f"共有 {total_tasks} 个提取任务")

            success_count = 0
            for i, (task_name, cmd) in enumerate(tasks, 1):
                self.log(f"\n[{i}/{total_tasks}] 开始提取: {task_name}")
                self.log(f"执行命令: {' '.join(cmd)}")

                # 创建任务特定的输出目录
                task_output_dir = os.path.dirname(cmd[2])
                os.makedirs(task_output_dir, exist_ok=True)

                # 运行命令
                process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    text=True,
                    encoding='utf-8',
                    errors='replace'
                )

                # 实时输出日志
                for line in process.stdout:
                    if line.strip():
                        self.log(line.strip())

                process.wait()

                if process.returncode == 0:
                    self.log(f"✅ {task_name} 提取完成!")
                    success_count += 1
                else:
                    self.log(f"❌ {task_name} 提取失败，退出代码: {process.returncode}")

            # 显示最终结果
            if success_count == total_tasks:
                self.log(f"\n🎉 所有任务完成! ({success_count}/{total_tasks})")
                messagebox.showinfo("完成", f"所有资源提取完成!\n成功: {success_count}/{total_tasks}\n输出位置: {self.output_path.get()}")
            else:
                self.log(f"\n⚠️ 部分任务完成 ({success_count}/{total_tasks})")
                messagebox.showwarning("部分完成", f"部分资源提取完成\n成功: {success_count}/{total_tasks}\n输出位置: {self.output_path.get()}")

        except Exception as e:
            self.log(f"❌ 发生错误: {str(e)}")
            messagebox.showerror("错误", f"发生错误: {str(e)}")

        finally:
            self.progress.stop()
    
    def start_extraction(self):
        """开始提取（在新线程中运行）"""
        if not self.validate_inputs():
            return
        
        self.progress.start()
        self.log("=" * 50)
        self.log("开始新的提取任务")
        
        # 在新线程中运行提取过程
        thread = threading.Thread(target=self.run_extraction)
        thread.daemon = True
        thread.start()
    
    def open_gui(self):
        """打开 AssetStudio GUI 版本"""
        gui_path = "./AssetStudio/AssetStudio.GUI.exe"
        if os.path.exists(gui_path):
            try:
                subprocess.Popen([gui_path])
                self.log("已启动 AssetStudio GUI 版本")
                messagebox.showinfo("提示", "GUI 版本已启动\n请在界面中选择游戏为 GI (Genshin Impact)")
            except Exception as e:
                messagebox.showerror("错误", f"无法启动 GUI 版本: {str(e)}")
        else:
            messagebox.showerror("错误", "找不到 AssetStudio.GUI.exe")

def main():
    root = tk.Tk()
    app = GenshinExtractor(root)
    root.mainloop()

if __name__ == "__main__":
    main()
