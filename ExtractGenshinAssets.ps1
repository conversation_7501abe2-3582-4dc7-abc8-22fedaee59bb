# 原神资源提取脚本
# 使用 RazTools Studio 提取原神游戏资源

param(
    [Parameter(Mandatory=$false)]
    [string]$GenshinPath = "",
    
    [Parameter(Mandatory=$false)]
    [string]$OutputPath = ".\GenshinOutput",
    
    [Parameter(Mandatory=$false)]
    [string]$ResourceType = "All",
    
    [Parameter(Mandatory=$false)]
    [string]$Filter = ""
)

# 颜色输出函数
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

Write-ColorOutput Green "=== 原神资源提取工具 ==="
Write-ColorOutput Green "基于 RazTools Studio v1.36.00"
Write-Output ""

# 检查 AssetStudio 是否存在
if (-not (Test-Path ".\AssetStudio\AssetStudio.CLI.exe")) {
    Write-ColorOutput Red "错误: 找不到 AssetStudio.CLI.exe"
    Write-ColorOutput Red "请确保 AssetStudio 已正确解压到 .\AssetStudio\ 目录"
    exit 1
}

# 自动查找原神安装路径
$possiblePaths = @(
    "C:\Program Files\Genshin Impact\Genshin Impact Game\YuanShen_Data\StreamingAssets\AssetBundles",
    "C:\Program Files\Genshin Impact\Genshin Impact Game\GenshinImpact_Data\StreamingAssets\AssetBundles",
    "D:\Program Files\Genshin Impact\Genshin Impact Game\YuanShen_Data\StreamingAssets\AssetBundles",
    "D:\Program Files\Genshin Impact\Genshin Impact Game\GenshinImpact_Data\StreamingAssets\AssetBundles",
    "C:\Genshin Impact\Genshin Impact Game\YuanShen_Data\StreamingAssets\AssetBundles",
    "C:\Genshin Impact\Genshin Impact Game\GenshinImpact_Data\StreamingAssets\AssetBundles"
)

if ($GenshinPath -eq "") {
    Write-ColorOutput Yellow "正在自动查找原神安装路径..."
    foreach ($path in $possiblePaths) {
        if (Test-Path $path) {
            $GenshinPath = $path
            Write-ColorOutput Green "找到原神资源路径: $GenshinPath"
            break
        }
    }
    
    if ($GenshinPath -eq "") {
        Write-ColorOutput Red "未找到原神安装路径，请手动指定:"
        Write-ColorOutput Yellow "使用方法: .\ExtractGenshinAssets.ps1 -GenshinPath '你的原神路径'"
        Write-Output ""
        Write-ColorOutput Yellow "常见路径示例:"
        foreach ($path in $possiblePaths) {
            Write-Output "  $path"
        }
        exit 1
    }
}

# 验证路径
if (-not (Test-Path $GenshinPath)) {
    Write-ColorOutput Red "错误: 指定的原神路径不存在: $GenshinPath"
    exit 1
}

# 创建输出目录
if (-not (Test-Path $OutputPath)) {
    New-Item -ItemType Directory -Path $OutputPath -Force | Out-Null
    Write-ColorOutput Green "创建输出目录: $OutputPath"
}

Write-Output ""
Write-ColorOutput Cyan "配置信息:"
Write-Output "  原神路径: $GenshinPath"
Write-Output "  输出路径: $OutputPath"
Write-Output "  资源类型: $ResourceType"
if ($Filter -ne "") {
    Write-Output "  过滤器: $Filter"
}
Write-Output ""

# 根据资源类型构建命令
$baseCmd = ".\AssetStudio\AssetStudio.CLI.exe"
$args = @("`"$GenshinPath`"", "`"$OutputPath`"", "--game", "GI")

switch ($ResourceType.ToLower()) {
    "textures" {
        $args += @("--types", "Texture2D")
        $outputSubDir = Join-Path $OutputPath "Textures"
    }
    "models" {
        $args += @("--types", "Mesh")
        $outputSubDir = Join-Path $OutputPath "Models"
    }
    "audio" {
        $args += @("--types", "AudioClip")
        $outputSubDir = Join-Path $OutputPath "Audio"
    }
    "animations" {
        $args += @("--types", "AnimationClip")
        $outputSubDir = Join-Path $OutputPath "Animations"
    }
    "materials" {
        $args += @("--types", "Material")
        $outputSubDir = Join-Path $OutputPath "Materials"
    }
    "sprites" {
        $args += @("--types", "Sprite")
        $outputSubDir = Join-Path $OutputPath "Sprites"
    }
    "fonts" {
        $args += @("--types", "Font")
        $outputSubDir = Join-Path $OutputPath "Fonts"
    }
    "shaders" {
        $args += @("--types", "Shader")
        $outputSubDir = Join-Path $OutputPath "Shaders"
    }
    "assetmap" {
        $args += @("--map_op", "AssetMap", "--map_type", "JSON")
        $outputSubDir = $OutputPath
    }
    "all" {
        # 不添加类型限制，提取所有资源
        $outputSubDir = $OutputPath
    }
    default {
        $args += @("--types", $ResourceType)
        $outputSubDir = $OutputPath
    }
}

# 更新输出路径
if ($ResourceType.ToLower() -ne "all" -and $ResourceType.ToLower() -ne "assetmap") {
    $args[1] = "`"$outputSubDir`""
    if (-not (Test-Path $outputSubDir)) {
        New-Item -ItemType Directory -Path $outputSubDir -Force | Out-Null
    }
}

# 添加过滤器
if ($Filter -ne "") {
    $args += @("--names", $Filter)
}

# 执行命令
Write-ColorOutput Yellow "开始提取资源..."
Write-ColorOutput Gray "执行命令: $baseCmd $($args -join ' ')"
Write-Output ""

$startTime = Get-Date
& $baseCmd @args

$endTime = Get-Date
$duration = $endTime - $startTime

Write-Output ""
if ($LASTEXITCODE -eq 0) {
    Write-ColorOutput Green "✅ 资源提取完成!"
    Write-ColorOutput Green "耗时: $($duration.ToString('hh\:mm\:ss'))"
    Write-ColorOutput Green "输出位置: $outputSubDir"
} else {
    Write-ColorOutput Red "❌ 资源提取失败，退出代码: $LASTEXITCODE"
}

Write-Output ""
Write-ColorOutput Cyan "提示: 可以使用以下命令提取特定类型的资源:"
Write-Output "  .\ExtractGenshinAssets.ps1 -ResourceType Textures"
Write-Output "  .\ExtractGenshinAssets.ps1 -ResourceType Models"
Write-Output "  .\ExtractGenshinAssets.ps1 -ResourceType Audio"
Write-Output "  .\ExtractGenshinAssets.ps1 -ResourceType Animations"
Write-Output "  .\ExtractGenshinAssets.ps1 -ResourceType AssetMap"
