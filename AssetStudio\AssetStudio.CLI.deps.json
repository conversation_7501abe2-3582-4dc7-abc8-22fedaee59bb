{"runtimeTarget": {"name": ".NETCoreApp,Version=v7.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v7.0": {"AssetStudio.CLI/1.36.00": {"dependencies": {"AssetStudio": "1.36.0", "AssetStudio.Utility": "1.36.0", "Newtonsoft.Json": "13.0.3", "System.CommandLine": "2.0.0-beta4.22272.1", "System.Configuration.ConfigurationManager": "8.0.0-preview.5.23280.8"}, "runtime": {"AssetStudio.CLI.dll": {}}}, "Kyaru.Texture2DDecoder/0.17.0": {"runtime": {"lib/net5.0/Texture2DDecoderWrapper.dll": {"assemblyVersion": "0.17.0.0", "fileVersion": "0.17.0.0"}}}, "Kyaru.Texture2DDecoder.Windows/0.1.0": {"runtimeTargets": {"runtimes/win-arm64/native/Texture2DDecoderNative.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/Texture2DDecoderNative.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/Texture2DDecoderNative.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "MessagePack/2.6.100-alpha": {"dependencies": {"MessagePack.Annotations": "2.6.100-alpha", "Microsoft.NET.StringTools": "17.4.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/MessagePack.dll": {"assemblyVersion": "2.6.0.0", "fileVersion": "2.6.100.28020"}}}, "MessagePack.Annotations/2.6.100-alpha": {"runtime": {"lib/netstandard2.0/MessagePack.Annotations.dll": {"assemblyVersion": "2.6.0.0", "fileVersion": "2.6.100.28020"}}}, "Microsoft.NET.StringTools/17.4.0": {"dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net7.0/Microsoft.NET.StringTools.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "17.4.0.51802"}}}, "Microsoft.NETCore.Platforms/5.0.0": {}, "Mono.Cecil/0.11.5": {"runtime": {"lib/netstandard2.0/Mono.Cecil.Mdb.dll": {"assemblyVersion": "0.11.5.0", "fileVersion": "0.11.5.0"}, "lib/netstandard2.0/Mono.Cecil.Pdb.dll": {"assemblyVersion": "0.11.5.0", "fileVersion": "0.11.5.0"}, "lib/netstandard2.0/Mono.Cecil.Rocks.dll": {"assemblyVersion": "0.11.5.0", "fileVersion": "0.11.5.0"}, "lib/netstandard2.0/Mono.Cecil.dll": {"assemblyVersion": "0.11.5.0", "fileVersion": "0.11.5.0"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.3.27908"}}}, "SharpGen.Runtime/2.1.2-beta": {"runtime": {"lib/net7.0/SharpGen.Runtime.dll": {"assemblyVersion": "2.1.2.0", "fileVersion": "2.1.2.0"}}}, "SharpGen.Runtime.COM/2.1.2-beta": {"dependencies": {"SharpGen.Runtime": "2.1.2-beta"}, "runtime": {"lib/net7.0/SharpGen.Runtime.COM.dll": {"assemblyVersion": "2.1.2.0", "fileVersion": "2.1.2.0"}}}, "SixLabors.Fonts/1.0.0-beta18": {"runtime": {"lib/netcoreapp3.1/SixLabors.Fonts.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "SixLabors.ImageSharp/2.1.3": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encoding.CodePages": "5.0.0"}, "runtime": {"lib/netcoreapp3.1/SixLabors.ImageSharp.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.1.3.0"}}}, "SixLabors.ImageSharp.Drawing/1.0.0-beta15": {"dependencies": {"SixLabors.Fonts": "1.0.0-beta18", "SixLabors.ImageSharp": "2.1.3"}, "runtime": {"lib/netcoreapp3.1/SixLabors.ImageSharp.Drawing.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "System.CommandLine/2.0.0-beta4.22272.1": {"runtime": {"lib/net6.0/System.CommandLine.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.0.22.27201"}}, "resources": {"lib/net6.0/cs/System.CommandLine.resources.dll": {"locale": "cs"}, "lib/net6.0/de/System.CommandLine.resources.dll": {"locale": "de"}, "lib/net6.0/es/System.CommandLine.resources.dll": {"locale": "es"}, "lib/net6.0/fr/System.CommandLine.resources.dll": {"locale": "fr"}, "lib/net6.0/it/System.CommandLine.resources.dll": {"locale": "it"}, "lib/net6.0/ja/System.CommandLine.resources.dll": {"locale": "ja"}, "lib/net6.0/ko/System.CommandLine.resources.dll": {"locale": "ko"}, "lib/net6.0/pl/System.CommandLine.resources.dll": {"locale": "pl"}, "lib/net6.0/pt-BR/System.CommandLine.resources.dll": {"locale": "pt-BR"}, "lib/net6.0/ru/System.CommandLine.resources.dll": {"locale": "ru"}, "lib/net6.0/tr/System.CommandLine.resources.dll": {"locale": "tr"}, "lib/net6.0/zh-Hans/System.CommandLine.resources.dll": {"locale": "zh-Hans"}, "lib/net6.0/zh-Hant/System.CommandLine.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.Configuration.ConfigurationManager/8.0.0-preview.5.23280.8": {"dependencies": {"System.Diagnostics.EventLog": "8.0.0-preview.5.23280.8", "System.Security.Cryptography.ProtectedData": "8.0.0-preview.5.23280.8"}, "runtime": {"lib/net7.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.28008"}}}, "System.Diagnostics.EventLog/8.0.0-preview.5.23280.8": {"runtime": {"lib/net7.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.28008"}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Diagnostics.EventLog.Messages.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "8.0.0.0", "fileVersion": "0.0.0.0"}, "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.28008"}}}, "System.Memory/4.5.5": {}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Security.Cryptography.ProtectedData/8.0.0-preview.5.23280.8": {"runtime": {"lib/net7.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.28008"}}}, "System.Text.Encoding.CodePages/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0"}}, "Vortice.D3DCompiler/3.4.3-beta": {"dependencies": {"SharpGen.Runtime": "2.1.2-beta", "Vortice.DirectX": "3.4.3-beta"}, "runtime": {"lib/net7.0/Vortice.D3DCompiler.dll": {"assemblyVersion": "3.4.3.0", "fileVersion": "3.4.3.0"}}}, "Vortice.DirectX/3.4.3-beta": {"dependencies": {"SharpGen.Runtime": "2.1.2-beta", "SharpGen.Runtime.COM": "2.1.2-beta", "Vortice.Mathematics": "1.7.6"}, "runtime": {"lib/net7.0/Vortice.DirectX.dll": {"assemblyVersion": "3.4.3.0", "fileVersion": "3.4.3.0"}}}, "Vortice.Mathematics/1.7.6": {"runtime": {"lib/net7.0/Vortice.Mathematics.dll": {"assemblyVersion": "1.7.6.0", "fileVersion": "1.7.6.0"}}}, "ZstdSharp.Port/0.7.2": {"runtime": {"lib/net7.0/ZstdSharp.dll": {"assemblyVersion": "0.7.2.0", "fileVersion": "0.7.2.0"}}}, "AssetStudio/1.36.0": {"dependencies": {"MessagePack": "2.6.100-alpha", "Newtonsoft.Json": "13.0.3", "ZstdSharp.Port": "0.7.2"}, "runtime": {"AssetStudio.dll": {}}}, "AssetStudio.FBXWrapper/1.36.0": {"dependencies": {"AssetStudio": "1.36.0", "AssetStudio.PInvoke": "1.36.0"}, "runtime": {"AssetStudio.FBXWrapper.dll": {}}}, "AssetStudio.PInvoke/1.36.0": {"runtime": {"AssetStudio.PInvoke.dll": {}}}, "AssetStudio.Utility/1.36.0": {"dependencies": {"AssetStudio": "1.36.0", "AssetStudio.FBXWrapper": "1.36.0", "AssetStudio.PInvoke": "1.36.0", "Kyaru.Texture2DDecoder": "0.17.0", "Kyaru.Texture2DDecoder.Windows": "0.1.0", "Mono.Cecil": "0.11.5", "SixLabors.ImageSharp.Drawing": "1.0.0-beta15", "Vortice.D3DCompiler": "3.4.3-beta"}, "runtime": {"AssetStudio.Utility.dll": {}}}}}, "libraries": {"AssetStudio.CLI/1.36.00": {"type": "project", "serviceable": false, "sha512": ""}, "Kyaru.Texture2DDecoder/0.17.0": {"type": "package", "serviceable": true, "sha512": "sha512-HbIwq7qqRJbfP3WT8EaJ9kIz8i1LW0Hr70UqPdPZ+Xb48gCwx/JYv3oBj3hm2KVU0+etnnZFOv19QfznsgRUUw==", "path": "kyaru.texture2ddecoder/0.17.0", "hashPath": "kyaru.texture2ddecoder.0.17.0.nupkg.sha512"}, "Kyaru.Texture2DDecoder.Windows/0.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-zAX95ybCio7xrrXaa3LkFMWoDxrwiasccXSZ6A3KykOWlgMDaZzyJdPp52uyubtN9CIxWdnD+u+aWvKvHHmCsw==", "path": "kyaru.texture2ddecoder.windows/0.1.0", "hashPath": "kyaru.texture2ddecoder.windows.0.1.0.nupkg.sha512"}, "MessagePack/2.6.100-alpha": {"type": "package", "serviceable": true, "sha512": "sha512-gAyxBDzMDLWVg8GGP3YS4M2WnpglwcZzEld9HEsqVYNsTtVELZidINr/TTxVyfnHXxyEiZy04czZDdq8Q02hQw==", "path": "messagepack/2.6.100-alpha", "hashPath": "messagepack.2.6.100-alpha.nupkg.sha512"}, "MessagePack.Annotations/2.6.100-alpha": {"type": "package", "serviceable": true, "sha512": "sha512-hl8OTk87/i4nMHtBasV+GRQe8g3tze3l6NGYmL4XOQwCATKLAFAIKRbaxxWAPo0wk26UPqJ7oC+Lg8fLQNWLAQ==", "path": "messagepack.annotations/2.6.100-alpha", "hashPath": "messagepack.annotations.2.6.100-alpha.nupkg.sha512"}, "Microsoft.NET.StringTools/17.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-06T6Hqfs3JDIaBvJaBRFFMIdU7oE0OMab5Xl8LKQjWPxBQr3BgVFKMQPTC+GsSEuYREWmK6g5eOd7Xqd9p1YCA==", "path": "microsoft.net.stringtools/17.4.0", "hashPath": "microsoft.net.stringtools.17.4.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ==", "path": "microsoft.netcore.platforms/5.0.0", "hashPath": "microsoft.netcore.platforms.5.0.0.nupkg.sha512"}, "Mono.Cecil/0.11.5": {"type": "package", "serviceable": true, "sha512": "sha512-fxfX+0JGTZ8YQeu1MYjbBiK2CYTSzDyEeIixt+yqKKTn7FW8rv7JMY70qevup4ZJfD7Kk/VG/jDzQQTpfch87g==", "path": "mono.cecil/0.11.5", "hashPath": "mono.cecil.0.11.5.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "SharpGen.Runtime/2.1.2-beta": {"type": "package", "serviceable": true, "sha512": "sha512-nqZAjfEG1jX1ivvdZLsi6Pkt0DiOJyuOgRgldNFsmjXFPhxUbXQibofLSwuDZidL2kkmtTF8qLoRIeqeVdXgYw==", "path": "sharpgen.runtime/2.1.2-beta", "hashPath": "sharpgen.runtime.2.1.2-beta.nupkg.sha512"}, "SharpGen.Runtime.COM/2.1.2-beta": {"type": "package", "serviceable": true, "sha512": "sha512-HBCrb6HfnUWx9v5/GjJeBr5DuodZLnHlFQQYXPrQs1Hbe1c6Wd0uCXf+SJp4hW8fQNxjXEu0FgiyHGlA/SRzRw==", "path": "sharpgen.runtime.com/2.1.2-beta", "hashPath": "sharpgen.runtime.com.2.1.2-beta.nupkg.sha512"}, "SixLabors.Fonts/1.0.0-beta18": {"type": "package", "serviceable": true, "sha512": "sha512-evykNmy/kEE9EAEKgZm3MNUYXuMHFfmcLUNPw7Ho5q7OI96GFkkIxBm+QaKOTPBKw+L0AjKOs+ArVg8P40Ac9g==", "path": "sixlabors.fonts/1.0.0-beta18", "hashPath": "sixlabors.fonts.1.0.0-beta18.nupkg.sha512"}, "SixLabors.ImageSharp/2.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-8yonNRWX3vUE9k29ta0Hbfa0AEc0hbDjSH/nZ3vOTJEXmY6hLnGsjDKoz96Z+AgOsrdkAu6PdL/Ebaf70aitzw==", "path": "sixlabors.imagesharp/2.1.3", "hashPath": "sixlabors.imagesharp.2.1.3.nupkg.sha512"}, "SixLabors.ImageSharp.Drawing/1.0.0-beta15": {"type": "package", "serviceable": true, "sha512": "sha512-HMb/JNtNsSOr0lh71s5jD4Bv7sUwTxhGz2fOtuXcInQ4p9SB+1oEY3I6PrKXHpOCKaYArLD5r/4mA8t0dT/M5A==", "path": "sixlabors.imagesharp.drawing/1.0.0-beta15", "hashPath": "sixlabors.imagesharp.drawing.1.0.0-beta15.nupkg.sha512"}, "System.CommandLine/2.0.0-beta4.22272.1": {"type": "package", "serviceable": true, "sha512": "sha512-1uqED/q2H0kKoLJ4+hI2iPSBSEdTuhfCYADeJrAqERmiGQ2NNacYKRNEQ+gFbU4glgVyK8rxI+ZOe1onEtr/Pg==", "path": "system.commandline/2.0.0-beta4.22272.1", "hashPath": "system.commandline.2.0.0-beta4.22272.1.nupkg.sha512"}, "System.Configuration.ConfigurationManager/8.0.0-preview.5.23280.8": {"type": "package", "serviceable": true, "sha512": "sha512-HRlPUWAZbi9zJ7kkXeCz50907A4AYN4VawLiYNu0WkQHhr7HH9s3DVKDZMGMEdPZmwmg4hTQma7Yz5y/H64VuA==", "path": "system.configuration.configurationmanager/8.0.0-preview.5.23280.8", "hashPath": "system.configuration.configurationmanager.8.0.0-preview.5.23280.8.nupkg.sha512"}, "System.Diagnostics.EventLog/8.0.0-preview.5.23280.8": {"type": "package", "serviceable": true, "sha512": "sha512-rwL10IplmGRKPEj/GNOqmTmnmPZTJCYL6N362vYOQF97FkGFMU6GIlXn1yp5BIW6vo/hLHHjxoBpN6etcClW7w==", "path": "system.diagnostics.eventlog/8.0.0-preview.5.23280.8", "hashPath": "system.diagnostics.eventlog.8.0.0-preview.5.23280.8.nupkg.sha512"}, "System.Memory/4.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "path": "system.memory/4.5.5", "hashPath": "system.memory.4.5.5.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/8.0.0-preview.5.23280.8": {"type": "package", "serviceable": true, "sha512": "sha512-ycCVCV54oZ26V/sZsEtqkhdQLnJnZQYH0YpqjhVN9btvMzh1/2sDVUvaCHw4mUp+lJ9j+sp+DC1FalHRIPvdxA==", "path": "system.security.cryptography.protecteddata/8.0.0-preview.5.23280.8", "hashPath": "system.security.cryptography.protecteddata.8.0.0-preview.5.23280.8.nupkg.sha512"}, "System.Text.Encoding.CodePages/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NyscU59xX6Uo91qvhOs2Ccho3AR2TnZPomo1Z0K6YpyztBPM/A5VbkzOO19sy3A3i1TtEnTxA7bCe3Us+r5MWg==", "path": "system.text.encoding.codepages/5.0.0", "hashPath": "system.text.encoding.codepages.5.0.0.nupkg.sha512"}, "Vortice.D3DCompiler/3.4.3-beta": {"type": "package", "serviceable": true, "sha512": "sha512-6Uh4Yktl4CeJ4sUBOblJk6MF/fhBj2QcN7qLu8k1OTlv50PZewQDKIN1DBVYcJYJk+Nn/tt7Jf1OToaKMSyrIw==", "path": "vortice.d3dcompiler/3.4.3-beta", "hashPath": "vortice.d3dcompiler.3.4.3-beta.nupkg.sha512"}, "Vortice.DirectX/3.4.3-beta": {"type": "package", "serviceable": true, "sha512": "sha512-onBgvs3nlYJJ07qny+Wr+O1qBrif/EtnQ9MDeTU7uNktnc2ZYvdUnN9lkZ4jMuwP49X0HORnCWG+9t2A7YatQQ==", "path": "vortice.directx/3.4.3-beta", "hashPath": "vortice.directx.3.4.3-beta.nupkg.sha512"}, "Vortice.Mathematics/1.7.6": {"type": "package", "serviceable": true, "sha512": "sha512-W8FNv850lPGxmHphwLyi1qnUlQHZBxh/62EenFJTaY6acPP29Fk0xMQJI60G+YNlsVJb3fSoriuW+ong5sM5UQ==", "path": "vortice.mathematics/1.7.6", "hashPath": "vortice.mathematics.1.7.6.nupkg.sha512"}, "ZstdSharp.Port/0.7.2": {"type": "package", "serviceable": true, "sha512": "sha512-mT6Pb7qzDGapJ2ic4dAFqbzHpM+qgnasQIGR/Sofrk0fmKTQSTljG55OFXHbDJczLZWnUAFsg4bKR81uHiKzPg==", "path": "zstdsharp.port/0.7.2", "hashPath": "zstdsharp.port.0.7.2.nupkg.sha512"}, "AssetStudio/1.36.0": {"type": "project", "serviceable": false, "sha512": ""}, "AssetStudio.FBXWrapper/1.36.0": {"type": "project", "serviceable": false, "sha512": ""}, "AssetStudio.PInvoke/1.36.0": {"type": "project", "serviceable": false, "sha512": ""}, "AssetStudio.Utility/1.36.0": {"type": "project", "serviceable": false, "sha512": ""}}}