# 简化版原神资源提取脚本
param(
    [string]$GenshinPath = "",
    [string]$OutputPath = ".\GenshinOutput",
    [string]$ResourceType = "All"
)

Write-Host "=== 原神资源提取工具 ===" -ForegroundColor Green
Write-Host "基于 RazTools Studio v1.36.00" -ForegroundColor Green
Write-Host ""

# 检查 AssetStudio 是否存在
if (-not (Test-Path ".\AssetStudio\AssetStudio.CLI.exe")) {
    Write-Host "错误: 找不到 AssetStudio.CLI.exe" -ForegroundColor Red
    Write-Host "请确保 AssetStudio 已正确解压到 .\AssetStudio\ 目录" -ForegroundColor Red
    exit 1
}

# 自动查找原神安装路径
$possiblePaths = @(
    "C:\Program Files\Genshin Impact\Genshin Impact Game\YuanShen_Data\StreamingAssets\AssetBundles",
    "C:\Program Files\Genshin Impact\Genshin Impact Game\GenshinImpact_Data\StreamingAssets\AssetBundles",
    "D:\Program Files\Genshin Impact\Genshin Impact Game\YuanShen_Data\StreamingAssets\AssetBundles",
    "D:\Program Files\Genshin Impact\Genshin Impact Game\GenshinImpact_Data\StreamingAssets\AssetBundles"
)

if ($GenshinPath -eq "") {
    Write-Host "正在自动查找原神安装路径..." -ForegroundColor Yellow
    foreach ($path in $possiblePaths) {
        if (Test-Path $path) {
            $GenshinPath = $path
            Write-Host "找到原神资源路径: $GenshinPath" -ForegroundColor Green
            break
        }
    }
    
    if ($GenshinPath -eq "") {
        Write-Host "未找到原神安装路径，请手动指定" -ForegroundColor Red
        Write-Host "使用方法: .\ExtractGenshin_Simple.ps1 -GenshinPath '你的原神路径'" -ForegroundColor Yellow
        exit 1
    }
}

# 验证路径
if (-not (Test-Path $GenshinPath)) {
    Write-Host "错误: 指定的原神路径不存在: $GenshinPath" -ForegroundColor Red
    exit 1
}

# 创建输出目录
if (-not (Test-Path $OutputPath)) {
    New-Item -ItemType Directory -Path $OutputPath -Force | Out-Null
    Write-Host "创建输出目录: $OutputPath" -ForegroundColor Green
}

Write-Host ""
Write-Host "配置信息:" -ForegroundColor Cyan
Write-Host "  原神路径: $GenshinPath"
Write-Host "  输出路径: $OutputPath"
Write-Host "  资源类型: $ResourceType"
Write-Host ""

# 构建命令参数
$args = @("`"$GenshinPath`"", "`"$OutputPath`"", "--game", "GI")

switch ($ResourceType.ToLower()) {
    "textures" { $args += @("--types", "Texture2D") }
    "models" { $args += @("--types", "Mesh") }
    "audio" { $args += @("--types", "AudioClip") }
    "animations" { $args += @("--types", "AnimationClip") }
    "materials" { $args += @("--types", "Material") }
    "sprites" { $args += @("--types", "Sprite") }
    "assetmap" { $args += @("--map_op", "AssetMap", "--map_type", "JSON") }
}

# 执行命令
Write-Host "开始提取资源..." -ForegroundColor Yellow
Write-Host "执行命令: .\AssetStudio\AssetStudio.CLI.exe $($args -join ' ')" -ForegroundColor Gray
Write-Host ""

$startTime = Get-Date
& ".\AssetStudio\AssetStudio.CLI.exe" @args
$endTime = Get-Date
$duration = $endTime - $startTime

Write-Host ""
if ($LASTEXITCODE -eq 0) {
    Write-Host "资源提取完成!" -ForegroundColor Green
    Write-Host "耗时: $($duration.ToString('hh\:mm\:ss'))" -ForegroundColor Green
    Write-Host "输出位置: $OutputPath" -ForegroundColor Green
} else {
    Write-Host "资源提取失败，退出代码: $LASTEXITCODE" -ForegroundColor Red
}

Write-Host ""
Write-Host "提示: 可以使用以下命令提取特定类型的资源:" -ForegroundColor Cyan
Write-Host "  .\ExtractGenshin_Simple.ps1 -ResourceType Textures"
Write-Host "  .\ExtractGenshin_Simple.ps1 -ResourceType Models"
Write-Host "  .\ExtractGenshin_Simple.ps1 -ResourceType Audio"
Write-Host "  .\ExtractGenshin_Simple.ps1 -ResourceType AssetMap"
