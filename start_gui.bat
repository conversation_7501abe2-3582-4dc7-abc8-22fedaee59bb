@echo off
echo 启动原神资源提取工具 (Python GUI版本)
echo.

REM 检查 Python 是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到 Python
    echo 请先安装 Python 3.6 或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 检查 tkinter 是否可用
python -c "import tkinter" >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: tkinter 模块不可用
    echo 请确保安装了完整的 Python 包（包含 tkinter）
    pause
    exit /b 1
)

REM 启动 GUI
echo 启动中...
python genshin_extractor.py

if %errorlevel% neq 0 (
    echo.
    echo 程序异常退出
    pause
)
