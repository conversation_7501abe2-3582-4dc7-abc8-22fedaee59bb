﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
	<appSettings>
		<add key="exportAll" value="False" />
		<add key="openAfterExport" value="True" />
		<add key="convertTexture" value="True" />
		<add key="convertAudio" value="True" />
		<add key="convertType" value="Png" />
		<add key="eulerFilter" value="True" />
		<add key="filterPrecision" value="0.25" />
		<add key="exportAllNodes" value="True" />
		<add key="exportSkins" value="True" />
		<add key="exportMaterials" value="False" />
		<add key="exportAnimations" value="True" />
		<add key="boneSize" value="10" />
		<add key="fbxVersion" value="3" />
		<add key="fbxFormat" value="0" />
		<add key="scaleFactor" value="1" />
		<add key="exportBlendShape" value="True" />
		<add key="castToBone" value="False" />
		<add key="restoreExtensionName" value="True" />
		<add key="enableFileLogging" value="False" />
		<add key="minimalAssetMap" value="True" />
		<add key="allowDuplicates" value="False" />
		<add key="texs" value='{}' />
		<add key="uvs" value='{"UV0":{"Item1":true,"Item2":0},"UV1":{"Item1":true,"Item2":1},"UV2":{"Item1":false,"Item2":0},"UV3":{"Item1":false,"Item2":0},"UV4":{"Item1":false,"Item2":0},"UV5":{"Item1":false,"Item2":0},"UV6":{"Item1":false,"Item2":0},"UV7":{"Item1":false,"Item2":0}}' />
		<add key="types" value='{"Animation":{"Item1":true,"Item2":false},"AnimationClip":{"Item1":true,"Item2":true},"Animator":{"Item1":true,"Item2":true},"AnimatorController":{"Item1":true,"Item2":false},"AnimatorOverrideController":{"Item1":true,"Item2":false},"AssetBundle":{"Item1":true,"Item2":false},"AudioClip":{"Item1":true,"Item2":true},"Avatar":{"Item1":true,"Item2":false},"Font":{"Item1":true,"Item2":true},"GameObject":{"Item1":true,"Item2":false},"IndexObject":{"Item1":true,"Item2":false},"Material":{"Item1":true,"Item2":true},"Mesh":{"Item1":true,"Item2":true},"MeshFilter":{"Item1":true,"Item2":false},"MeshRenderer":{"Item1":true,"Item2":false},"MiHoYoBinData":{"Item1":true,"Item2":true},"MonoBehaviour":{"Item1":true,"Item2":true},"MonoScript":{"Item1":true,"Item2":false},"MovieTexture":{"Item1":true,"Item2":true},"PlayerSettings":{"Item1":true,"Item2":false},"RectTransform":{"Item1":true,"Item2":false},"Shader":{"Item1":true,"Item2":true},"SkinnedMeshRenderer":{"Item1":true,"Item2":false},"Sprite":{"Item1":true,"Item2":true},"SpriteAtlas":{"Item1":true,"Item2":false},"TextAsset":{"Item1":true,"Item2":true},"Texture2D":{"Item1":true,"Item2":true},"Transform":{"Item1":true,"Item2":false},"VideoClip":{"Item1":true,"Item2":true},"ResourceManager":{"Item1":true,"Item2":false}}' />
	</appSettings>
</configuration>