# 原神资源解包指南 - RazTools Studio

## 工具概述
RazTools Studio 是一个专门针对 miHoYo 游戏（包括原神）优化的 AssetStudio 修改版，支持解包和提取游戏资源。

## 安装完成
✅ AssetStudio v1.36.00 已安装在: `./AssetStudio/`
✅ .NET 7.0 运行时已安装
✅ 输出目录已创建: `./GenshinOutput/`

## 原神资源文件位置
原神的资源文件通常位于以下位置：

### 官方客户端
```
C:\Program Files\Genshin Impact\Genshin Impact Game\YuanShen_Data\StreamingAssets\AssetBundles\
```

### 国际版
```
C:\Program Files\Genshin Impact\Genshin Impact Game\GenshinImpact_Data\StreamingAssets\AssetBundles\
```

### 自定义安装路径
如果您自定义了安装路径，请在相应目录下查找：
```
[安装路径]\Genshin Impact Game\[YuanShen_Data或GenshinImpact_Data]\StreamingAssets\AssetBundles\
```

## 使用方法

### 1. GUI 版本（推荐新手）
```powershell
# 启动图形界面
.\AssetStudio\AssetStudio.GUI.exe
```

在 GUI 中：
1. 点击 "File" -> "Load Folder"
2. 选择原神的 AssetBundles 文件夹
3. 在 "Options" -> "Export Options" 中选择游戏为 "GI" (Genshin Impact)
4. 选择要导出的资源类型
5. 点击 "Export" -> "All assets" 或选择特定资源

### 2. CLI 版本（批量处理）

#### 基本语法
```powershell
.\AssetStudio\AssetStudio.CLI.exe <输入路径> <输出路径> --game GI [其他选项]
```

#### 示例命令

##### 提取所有贴图
```powershell
.\AssetStudio\AssetStudio.CLI.exe "C:\Program Files\Genshin Impact\Genshin Impact Game\YuanShen_Data\StreamingAssets\AssetBundles" ".\GenshinOutput\Textures" --game GI --types Texture2D
```

##### 提取角色模型
```powershell
.\AssetStudio\AssetStudio.CLI.exe "C:\Program Files\Genshin Impact\Genshin Impact Game\YuanShen_Data\StreamingAssets\AssetBundles" ".\GenshinOutput\Models" --game GI --types Mesh --names ".*[Aa]vatar.*"
```

##### 提取音频文件
```powershell
.\AssetStudio\AssetStudio.CLI.exe "C:\Program Files\Genshin Impact\Genshin Impact Game\YuanShen_Data\StreamingAssets\AssetBundles" ".\GenshinOutput\Audio" --game GI --types AudioClip
```

##### 提取动画
```powershell
.\AssetStudio\AssetStudio.CLI.exe "C:\Program Files\Genshin Impact\Genshin Impact Game\YuanShen_Data\StreamingAssets\AssetBundles" ".\GenshinOutput\Animations" --game GI --types AnimationClip
```

##### 生成资源映射表
```powershell
.\AssetStudio\AssetStudio.CLI.exe "C:\Program Files\Genshin Impact\Genshin Impact Game\YuanShen_Data\StreamingAssets\AssetBundles" ".\GenshinOutput\Maps" --game GI --map_op AssetMap --map_type JSON
```

## 高级选项

### 过滤器选项
- `--names`: 使用正则表达式过滤资源名称
- `--containers`: 使用正则表达式过滤容器名称
- `--types`: 指定要提取的资源类型

### 导出选项
- `--export_type Convert`: 转换为常见格式（默认）
- `--export_type Raw`: 导出原始数据
- `--export_type JSON`: 导出为JSON格式
- `--export_type Dump`: 转储所有信息

### 分组选项
- `--group_assets ByType`: 按类型分组（默认）
- `--group_assets ByContainer`: 按容器分组
- `--group_assets BySource`: 按源文件分组

## 常用资源类型
- `Texture2D`: 2D贴图
- `Mesh`: 3D模型网格
- `Material`: 材质
- `AudioClip`: 音频文件
- `AnimationClip`: 动画剪辑
- `Shader`: 着色器
- `Sprite`: 精灵图像
- `Font`: 字体文件

## 注意事项
1. 确保有足够的磁盘空间，原神资源文件很大
2. 首次解包可能需要较长时间
3. 某些资源可能需要特定的解密密钥
4. 建议先用小范围测试，确认参数正确后再进行大规模提取

## 故障排除
- 如果遇到权限问题，请以管理员身份运行
- 如果某些文件无法解析，可能需要更新工具版本
- 对于加密的资源，可能需要使用 `--key` 参数提供解密密钥
