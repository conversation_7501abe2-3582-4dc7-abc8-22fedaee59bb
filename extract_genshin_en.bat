@echo off
echo === Genshin Impact Asset Extraction Tool ===
echo Based on RazTools Studio v1.36.00
echo.

REM Check if AssetStudio exists
if not exist "AssetStudio\AssetStudio.CLI.exe" (
    echo Error: AssetStudio.CLI.exe not found
    echo Please ensure AssetStudio is extracted to .\AssetStudio\ directory
    pause
    exit /b 1
)

REM Set default output directory
set OUTPUT_DIR=GenshinOutput
if not exist "%OUTPUT_DIR%" mkdir "%OUTPUT_DIR%"

REM Common Genshin Impact installation paths
set GENSHIN_PATH1=C:\Program Files\Genshin Impact\Genshin Impact Game\YuanShen_Data\StreamingAssets\AssetBundles
set GENSHIN_PATH2=C:\Program Files\Genshin Impact\Genshin Impact Game\GenshinImpact_Data\StreamingAssets\AssetBundles
set GENSHIN_PATH3=D:\Program Files\Genshin Impact\Genshin Impact Game\YuanShen_Data\StreamingAssets\AssetBundles
set GENSHIN_PATH4=D:\Program Files\Genshin Impact\Genshin Impact Game\GenshinImpact_Data\StreamingAssets\AssetBundles

REM Auto-detect Genshin path
set GENSHIN_PATH=
if exist "%GENSHIN_PATH1%" set GENSHIN_PATH=%GENSHIN_PATH1%
if exist "%GENSHIN_PATH2%" set GENSHIN_PATH=%GENSHIN_PATH2%
if exist "%GENSHIN_PATH3%" set GENSHIN_PATH=%GENSHIN_PATH3%
if exist "%GENSHIN_PATH4%" set GENSHIN_PATH=%GENSHIN_PATH4%

if "%GENSHIN_PATH%"=="" (
    echo Genshin Impact path not found automatically.
    echo Common paths:
    echo   %GENSHIN_PATH1%
    echo   %GENSHIN_PATH2%
    echo   %GENSHIN_PATH3%
    echo   %GENSHIN_PATH4%
    echo.
    set /p GENSHIN_PATH=Please enter Genshin AssetBundles path: 
)

if not exist "%GENSHIN_PATH%" (
    echo Error: Path does not exist: %GENSHIN_PATH%
    pause
    exit /b 1
)

echo.
echo Found Genshin path: %GENSHIN_PATH%
echo Output directory: %OUTPUT_DIR%
echo.

REM Show menu
echo Select resource type to extract:
echo 1. All resources
echo 2. Textures
echo 3. Models
echo 4. Audio
echo 5. Animations
echo 6. Materials
echo 7. Sprites
echo 8. Generate AssetMap
echo 9. Start GUI version
echo.

set /p choice=Enter choice (1-9): 

if "%choice%"=="1" (
    echo Extracting all resources...
    AssetStudio\AssetStudio.CLI.exe "%GENSHIN_PATH%" "%OUTPUT_DIR%" --game GI
) else if "%choice%"=="2" (
    echo Extracting textures...
    AssetStudio\AssetStudio.CLI.exe "%GENSHIN_PATH%" "%OUTPUT_DIR%\Textures" --game GI --types Texture2D
) else if "%choice%"=="3" (
    echo Extracting models...
    AssetStudio\AssetStudio.CLI.exe "%GENSHIN_PATH%" "%OUTPUT_DIR%\Models" --game GI --types Mesh
) else if "%choice%"=="4" (
    echo Extracting audio...
    AssetStudio\AssetStudio.CLI.exe "%GENSHIN_PATH%" "%OUTPUT_DIR%\Audio" --game GI --types AudioClip
) else if "%choice%"=="5" (
    echo Extracting animations...
    AssetStudio\AssetStudio.CLI.exe "%GENSHIN_PATH%" "%OUTPUT_DIR%\Animations" --game GI --types AnimationClip
) else if "%choice%"=="6" (
    echo Extracting materials...
    AssetStudio\AssetStudio.CLI.exe "%GENSHIN_PATH%" "%OUTPUT_DIR%\Materials" --game GI --types Material
) else if "%choice%"=="7" (
    echo Extracting sprites...
    AssetStudio\AssetStudio.CLI.exe "%GENSHIN_PATH%" "%OUTPUT_DIR%\Sprites" --game GI --types Sprite
) else if "%choice%"=="8" (
    echo Generating AssetMap...
    AssetStudio\AssetStudio.CLI.exe "%GENSHIN_PATH%" "%OUTPUT_DIR%" --game GI --map_op AssetMap --map_type JSON
) else if "%choice%"=="9" (
    echo Starting GUI version...
    start "" "AssetStudio\AssetStudio.GUI.exe"
    echo GUI started. Please select game as GI (Genshin Impact) in the interface.
) else (
    echo Invalid choice
    pause
    exit /b 1
)

echo.
if "%choice%" neq "9" (
    echo Extraction completed!
    echo Output location: %OUTPUT_DIR%
)
pause
