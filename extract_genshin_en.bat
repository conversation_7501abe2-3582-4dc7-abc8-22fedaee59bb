@echo off
echo === Genshin Impact Asset Extraction Tool ===
echo Based on RazTools Studio v1.36.00
echo.

REM Check if AssetStudio exists
if not exist "AssetStudio\AssetStudio.CLI.exe" (
    echo Error: AssetStudio.CLI.exe not found
    echo Please ensure AssetStudio is extracted to .\AssetStudio\ directory
    pause
    exit /b 1
)

REM Set default output directory
set OUTPUT_DIR=GenshinOutput
if not exist "%OUTPUT_DIR%" mkdir "%OUTPUT_DIR%"

REM Common Genshin Impact installation paths
set GENSHIN_PATH1=C:\Program Files\miHoYo Launcher\games\Genshin Impact Game\YuanShen_Data\StreamingAssets\AssetBundles
set GENSHIN_PATH2=C:\Program Files\Genshin Impact\Genshin Impact Game\YuanShen_Data\StreamingAssets\AssetBundles
set GENSHIN_PATH3=C:\Program Files\Genshin Impact\Genshin Impact Game\GenshinImpact_Data\StreamingAssets\AssetBundles
set GENSHIN_PATH4=D:\Program Files\Genshin Impact\Genshin Impact Game\YuanShen_Data\StreamingAssets\AssetBundles

REM Auto-detect Genshin path
set GENSHIN_PATH=
if exist "%GENSHIN_PATH1%" set GENSHIN_PATH=%GENSHIN_PATH1%
if exist "%GENSHIN_PATH2%" set GENSHIN_PATH=%GENSHIN_PATH2%
if exist "%GENSHIN_PATH3%" set GENSHIN_PATH=%GENSHIN_PATH3%
if exist "%GENSHIN_PATH4%" set GENSHIN_PATH=%GENSHIN_PATH4%

if "%GENSHIN_PATH%"=="" (
    echo Genshin Impact path not found automatically.
    echo Common paths:
    echo   %GENSHIN_PATH1%
    echo   %GENSHIN_PATH2%
    echo   %GENSHIN_PATH3%
    echo   %GENSHIN_PATH4%
    echo.
    set /p GENSHIN_PATH=Please enter Genshin AssetBundles path: 
)

if not exist "%GENSHIN_PATH%" (
    echo Error: Path does not exist: %GENSHIN_PATH%
    pause
    exit /b 1
)

echo.
echo Found Genshin path: %GENSHIN_PATH%
echo Output directory: %OUTPUT_DIR%
echo.

REM Show menu
echo Select extraction mode:
echo 1. Extract ALL resources (single option)
echo 2. Extract SPECIFIC resource types (multiple selection)
echo 3. Generate AssetMap only
echo 4. Start GUI version
echo.

set /p mode=Enter mode (1-4):

if "%mode%"=="1" (
    echo Extracting all resources...
    AssetStudio\AssetStudio.CLI.exe "%GENSHIN_PATH%" "%OUTPUT_DIR%" --game GI
) else if "%mode%"=="2" (
    call :SelectMultipleTypes
) else if "%mode%"=="3" (
    echo Generating AssetMap...
    AssetStudio\AssetStudio.CLI.exe "%GENSHIN_PATH%" "%OUTPUT_DIR%" --game GI --map_op AssetMap --map_type JSON
) else if "%mode%"=="4" (
    echo Starting GUI version...
    start "" "AssetStudio\AssetStudio.GUI.exe"
    echo GUI started. Please select game as GI (Genshin Impact) in the interface.
) else (
    echo Invalid choice
    pause
    exit /b 1
)

echo.
if "%mode%" neq "4" (
    echo Extraction completed!
    echo Output location: %OUTPUT_DIR%
)
pause
goto :eof

:SelectMultipleTypes
echo.
echo Select resource types to extract (enter Y/N for each):
echo.

set /p tex=Extract Textures (Y/N):
set /p mod=Extract Models (Y/N):
set /p aud=Extract Audio (Y/N):
set /p ani=Extract Animations (Y/N):
set /p mat=Extract Materials (Y/N):
set /p spr=Extract Sprites (Y/N):
set /p fon=Extract Fonts (Y/N):
set /p sha=Extract Shaders (Y/N):
set /p map=Generate AssetMap (Y/N):

echo.
echo Starting extraction of selected types...

if /i "%tex%"=="Y" (
    echo Extracting Textures...
    AssetStudio\AssetStudio.CLI.exe "%GENSHIN_PATH%" "%OUTPUT_DIR%\Textures" --game GI --types Texture2D
)

if /i "%mod%"=="Y" (
    echo Extracting Models...
    AssetStudio\AssetStudio.CLI.exe "%GENSHIN_PATH%" "%OUTPUT_DIR%\Models" --game GI --types Mesh
)

if /i "%aud%"=="Y" (
    echo Extracting Audio...
    AssetStudio\AssetStudio.CLI.exe "%GENSHIN_PATH%" "%OUTPUT_DIR%\Audio" --game GI --types AudioClip
)

if /i "%ani%"=="Y" (
    echo Extracting Animations...
    AssetStudio\AssetStudio.CLI.exe "%GENSHIN_PATH%" "%OUTPUT_DIR%\Animations" --game GI --types AnimationClip
)

if /i "%mat%"=="Y" (
    echo Extracting Materials...
    AssetStudio\AssetStudio.CLI.exe "%GENSHIN_PATH%" "%OUTPUT_DIR%\Materials" --game GI --types Material
)

if /i "%spr%"=="Y" (
    echo Extracting Sprites...
    AssetStudio\AssetStudio.CLI.exe "%GENSHIN_PATH%" "%OUTPUT_DIR%\Sprites" --game GI --types Sprite
)

if /i "%fon%"=="Y" (
    echo Extracting Fonts...
    AssetStudio\AssetStudio.CLI.exe "%GENSHIN_PATH%" "%OUTPUT_DIR%\Fonts" --game GI --types Font
)

if /i "%sha%"=="Y" (
    echo Extracting Shaders...
    AssetStudio\AssetStudio.CLI.exe "%GENSHIN_PATH%" "%OUTPUT_DIR%\Shaders" --game GI --types Shader
)

if /i "%map%"=="Y" (
    echo Generating AssetMap...
    AssetStudio\AssetStudio.CLI.exe "%GENSHIN_PATH%" "%OUTPUT_DIR%" --game GI --map_op AssetMap --map_type JSON
)

goto :eof
