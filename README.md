# 原神资源解包工具 - 安装完成

## 🎉 安装状态
✅ **RazTools Studio v1.36.00** 已成功安装  
✅ **.NET 7.0 运行时** 已安装  
✅ **工具脚本** 已创建  
✅ **输出目录** 已准备就绪  

## 📁 文件结构
```
d:\mihoyo\
├── AssetStudio\                    # RazTools Studio 主程序
│   ├── AssetStudio.CLI.exe        # 命令行版本
│   ├── AssetStudio.GUI.exe        # 图形界面版本
│   └── ...                        # 其他依赖文件
├── GenshinOutput\                  # 输出目录
├── extract_genshin_en.bat         # 英文批处理脚本 (推荐)
├── StartAssetStudioGUI.ps1         # GUI 启动脚本
├── GenshinAssetExtraction_Guide.md # 详细使用指南
└── README.md                       # 本文件
```

## 🚀 快速开始

### 方法一：使用批处理脚本 (推荐)
1. 双击运行 `extract_genshin_en.bat`
2. 如果自动检测失败，手动输入原神 AssetBundles 路径
3. 选择要提取的资源类型 (1-9)
4. 等待提取完成

### 方法二：使用 GUI 界面
1. 双击运行 `StartAssetStudioGUI.ps1` 或直接运行 `AssetStudio\AssetStudio.GUI.exe`
2. 在界面中：
   - 点击 `File` → `Load Folder`
   - 选择原神的 AssetBundles 文件夹
   - 在 `Options` → `Export Options` 中选择游戏为 `GI`
   - 选择要导出的资源并点击导出

### 方法三：使用命令行
```cmd
# 提取所有贴图
AssetStudio\AssetStudio.CLI.exe "原神路径" "GenshinOutput\Textures" --game GI --types Texture2D

# 提取角色模型
AssetStudio\AssetStudio.CLI.exe "原神路径" "GenshinOutput\Models" --game GI --types Mesh

# 生成资源映射表
AssetStudio\AssetStudio.CLI.exe "原神路径" "GenshinOutput" --game GI --map_op AssetMap --map_type JSON
```

## 📍 原神资源路径
工具会自动检测以下常见路径：
- `C:\Program Files\Genshin Impact\Genshin Impact Game\YuanShen_Data\StreamingAssets\AssetBundles`
- `C:\Program Files\Genshin Impact\Genshin Impact Game\GenshinImpact_Data\StreamingAssets\AssetBundles`
- `D:\Program Files\Genshin Impact\Genshin Impact Game\YuanShen_Data\StreamingAssets\AssetBundles`
- `D:\Program Files\Genshin Impact\Genshin Impact Game\GenshinImpact_Data\StreamingAssets\AssetBundles`

如果您的原神安装在其他位置，请手动指定路径。

## 🎯 支持的资源类型
1. **Textures** - 2D贴图文件
2. **Models** - 3D模型网格
3. **Audio** - 音频文件
4. **Animations** - 动画剪辑
5. **Materials** - 材质文件
6. **Sprites** - 精灵图像
7. **AssetMap** - 资源映射表 (用于分析)
8. **All** - 所有资源类型

## ⚡ 使用技巧
- **首次使用**：建议先生成 AssetMap 来了解资源结构
- **大量提取**：使用命令行版本效率更高
- **特定资源**：使用过滤器参数精确提取需要的文件
- **磁盘空间**：确保有足够空间，原神资源文件很大

## 🔧 高级用法
```cmd
# 使用正则表达式过滤角色相关资源
AssetStudio\AssetStudio.CLI.exe "原神路径" "输出路径" --game GI --types Texture2D --names ".*[Aa]vatar.*"

# 导出为原始格式
AssetStudio\AssetStudio.CLI.exe "原神路径" "输出路径" --game GI --export_type Raw

# 按容器分组
AssetStudio\AssetStudio.CLI.exe "原神路径" "输出路径" --game GI --group_assets ByContainer
```

## ❗ 注意事项
- 确保有足够的磁盘空间 (建议至少 10GB)
- 首次解包可能需要较长时间
- 某些加密资源可能需要特殊密钥
- 建议以管理员身份运行以避免权限问题

## 🆘 故障排除
- **找不到原神路径**：手动指定完整路径
- **权限错误**：以管理员身份运行
- **解析失败**：检查原神版本是否受支持
- **内存不足**：分批处理或增加虚拟内存

## 📚 更多信息
详细使用指南请查看 `GenshinAssetExtraction_Guide.md`

---
**工具版本**: RazTools Studio v1.36.00  
**支持游戏**: 原神 (Genshin Impact)  
**许可证**: MIT License  
**项目地址**: https://github.com/RazTools/Studio
