@echo off
chcp 65001 >nul
echo === 原神资源提取工具 ===
echo 基于 RazTools Studio v1.36.00
echo.

REM 检查 AssetStudio 是否存在
if not exist "AssetStudio\AssetStudio.CLI.exe" (
    echo 错误: 找不到 AssetStudio.CLI.exe
    echo 请确保 AssetStudio 已正确解压到 .\AssetStudio\ 目录
    pause
    exit /b 1
)

REM 设置默认输出目录
set OUTPUT_DIR=GenshinOutput
if not exist "%OUTPUT_DIR%" mkdir "%OUTPUT_DIR%"

REM 常见的原神安装路径
set GENSHIN_PATH1=C:\Program Files\Genshin Impact\Genshin Impact Game\YuanShen_Data\StreamingAssets\AssetBundles
set GENSHIN_PATH2=C:\Program Files\Genshin Impact\Genshin Impact Game\GenshinImpact_Data\StreamingAssets\AssetBundles
set GENSHIN_PATH3=D:\Program Files\Genshin Impact\Genshin Impact Game\YuanShen_Data\StreamingAssets\AssetBundles
set GENSHIN_PATH4=D:\Program Files\Genshin Impact\Genshin Impact Game\GenshinImpact_Data\StreamingAssets\AssetBundles

REM 自动查找原神路径
set GENSHIN_PATH=
if exist "%GENSHIN_PATH1%" set GENSHIN_PATH=%GENSHIN_PATH1%
if exist "%GENSHIN_PATH2%" set GENSHIN_PATH=%GENSHIN_PATH2%
if exist "%GENSHIN_PATH3%" set GENSHIN_PATH=%GENSHIN_PATH3%
if exist "%GENSHIN_PATH4%" set GENSHIN_PATH=%GENSHIN_PATH4%

if "%GENSHIN_PATH%"=="" (
    echo 未找到原神安装路径，请手动输入:
    echo 常见路径:
    echo   %GENSHIN_PATH1%
    echo   %GENSHIN_PATH2%
    echo   %GENSHIN_PATH3%
    echo   %GENSHIN_PATH4%
    echo.
    set /p GENSHIN_PATH=请输入原神 AssetBundles 路径: 
)

if not exist "%GENSHIN_PATH%" (
    echo 错误: 指定的路径不存在: %GENSHIN_PATH%
    pause
    exit /b 1
)

echo.
echo 找到原神路径: %GENSHIN_PATH%
echo 输出目录: %OUTPUT_DIR%
echo.

REM 显示菜单
echo 请选择要提取的资源类型:
echo 1. 所有资源 (All)
echo 2. 贴图 (Textures)
echo 3. 模型 (Models)
echo 4. 音频 (Audio)
echo 5. 动画 (Animations)
echo 6. 材质 (Materials)
echo 7. 精灵 (Sprites)
echo 8. 生成资源映射表 (AssetMap)
echo 9. 启动 GUI 版本
echo.

set /p choice=请输入选择 (1-9): 

if "%choice%"=="1" (
    echo 提取所有资源...
    AssetStudio\AssetStudio.CLI.exe "%GENSHIN_PATH%" "%OUTPUT_DIR%" --game GI
) else if "%choice%"=="2" (
    echo 提取贴图...
    AssetStudio\AssetStudio.CLI.exe "%GENSHIN_PATH%" "%OUTPUT_DIR%\Textures" --game GI --types Texture2D
) else if "%choice%"=="3" (
    echo 提取模型...
    AssetStudio\AssetStudio.CLI.exe "%GENSHIN_PATH%" "%OUTPUT_DIR%\Models" --game GI --types Mesh
) else if "%choice%"=="4" (
    echo 提取音频...
    AssetStudio\AssetStudio.CLI.exe "%GENSHIN_PATH%" "%OUTPUT_DIR%\Audio" --game GI --types AudioClip
) else if "%choice%"=="5" (
    echo 提取动画...
    AssetStudio\AssetStudio.CLI.exe "%GENSHIN_PATH%" "%OUTPUT_DIR%\Animations" --game GI --types AnimationClip
) else if "%choice%"=="6" (
    echo 提取材质...
    AssetStudio\AssetStudio.CLI.exe "%GENSHIN_PATH%" "%OUTPUT_DIR%\Materials" --game GI --types Material
) else if "%choice%"=="7" (
    echo 提取精灵...
    AssetStudio\AssetStudio.CLI.exe "%GENSHIN_PATH%" "%OUTPUT_DIR%\Sprites" --game GI --types Sprite
) else if "%choice%"=="8" (
    echo 生成资源映射表...
    AssetStudio\AssetStudio.CLI.exe "%GENSHIN_PATH%" "%OUTPUT_DIR%" --game GI --map_op AssetMap --map_type JSON
) else if "%choice%"=="9" (
    echo 启动 GUI 版本...
    start "" "AssetStudio\AssetStudio.GUI.exe"
    echo GUI 已启动，请在界面中选择游戏为 GI (Genshin Impact)
) else (
    echo 无效选择
    pause
    exit /b 1
)

echo.
if "%choice%" neq "9" (
    echo 提取完成！
    echo 输出位置: %OUTPUT_DIR%
)
pause
