# 启动 AssetStudio GUI
# 用于原神资源提取的图形界面工具

Write-Host "启动 AssetStudio GUI..." -ForegroundColor Green
Write-Host "用于原神资源提取" -ForegroundColor Yellow
Write-Host ""

# 检查文件是否存在
if (-not (Test-Path ".\AssetStudio\AssetStudio.GUI.exe")) {
    Write-Host "错误: 找不到 AssetStudio.GUI.exe" -ForegroundColor Red
    Write-Host "请确保 AssetStudio 已正确解压到 .\AssetStudio\ 目录" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

Write-Host "使用提示:" -ForegroundColor Cyan
Write-Host "1. 在 GUI 中点击 'File' -> 'Load Folder'" -ForegroundColor White
Write-Host "2. 选择原神的 AssetBundles 文件夹" -ForegroundColor White
Write-Host "3. 在 'Options' -> 'Export Options' 中选择游戏为 'GI'" -ForegroundColor White
Write-Host "4. 选择要导出的资源类型并导出" -ForegroundColor White
Write-Host ""

# 启动 GUI
Start-Process -FilePath ".\AssetStudio\AssetStudio.GUI.exe" -WorkingDirectory ".\AssetStudio"

Write-Host "AssetStudio GUI 已启动!" -ForegroundColor Green
